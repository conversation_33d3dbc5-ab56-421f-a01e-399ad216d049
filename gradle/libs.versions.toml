
[versions]
kotlin-version = "2.1.21"
ktor-version = "3.1.3"
logback-version = "1.5.13"
jdbc-version = "********"
postgresql = "42.7.2"
exposed-version = "0.43.0"

[libraries]
ktor-server-core = { module = "io.ktor:ktor-server-core", version.ref = "ktor-version" }
ktor-server-host-common = { module = "io.ktor:ktor-server-host-common", version.ref = "ktor-version" }
ktor-server-status-pages = { module = "io.ktor:ktor-server-status-pages", version.ref = "ktor-version" }
ktor-server-call-logging = { module = "io.ktor:ktor-server-call-logging", version.ref = "ktor-version" }
ktor-server-content-negotiation = { module = "io.ktor:ktor-server-content-negotiation", version.ref = "ktor-version" }
ktor-server-cors = { module = "io.ktor:ktor-server-cors", version.ref = "ktor-version" }
ktor-serialization-kotlinx-json = { module = "io.ktor:ktor-serialization-kotlinx-json", version.ref = "ktor-version" }
ktor-server-netty = { module = "io.ktor:ktor-server-netty", version.ref = "ktor-version" }
logback-classic = { module = "ch.qos.logback:logback-classic", version.ref = "logback-version" }
ktor-server-config-yaml = { module = "io.ktor:ktor-server-config-yaml", version.ref = "ktor-version" }
postgresql = { module = "org.postgresql:postgresql", version.ref = "postgresql" }
sqlite-jdbc = { module = "org.xerial:sqlite-jdbc", version.ref = "jdbc-version" }
exposed-core = { module = "org.jetbrains.exposed:exposed-core", version.ref = "exposed-version" }
exposed-dao = { module = "org.jetbrains.exposed:exposed-dao", version.ref = "exposed-version" }
exposed-jdbc = { module = "org.jetbrains.exposed:exposed-jdbc", version.ref = "exposed-version" }

ktor-server-test-host = { module = "io.ktor:ktor-server-test-host", version.ref = "ktor-version" }
kotlin-test-junit = { module = "org.jetbrains.kotlin:kotlin-test-junit", version.ref = "kotlin-version" }

[plugins]
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin-version" }
ktor = { id = "io.ktor.plugin", version.ref = "ktor-version" }
kotlin-plugin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin-version" }
