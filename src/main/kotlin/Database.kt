package com.gulderbone

import models.data.Items
import models.data.Orders
import models.data.OrderItems
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SchemaUtils
import org.jetbrains.exposed.sql.transactions.transaction

fun configureDatabase() {
    val driver = "org.postgresql.Driver"
    val localUrl = "******************************************"

    val remotePassword = System.getenv("POSTGRES_PASSWORD")
    val remoteHost = System.getenv("PGHOST")
    val remotePort = System.getenv("PGPORT")
    val remoteDatabase = System.getenv("PGDATABASE")
    val remoteUrl = remoteHost?.let { createJdbcUrl(it, remotePort ?: "5432", remoteDatabase ?: "railway") }

    val databaseUrl = remoteUrl ?: localUrl
    val password = remotePassword ?: "postgres"

    Database.connect(
        url = databaseUrl,
        driver = driver,
        user = "postgres",
        password = password
    )

    transaction {
        SchemaUtils.create(Items, Orders, OrderItems)
    }
}

fun createJdbcUrl(host: String, port: String, database: String = "railway"): String =
    "***************************************"