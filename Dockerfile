# Use OpenJDK 17 as the base image
FROM openjdk:17-jdk-slim

# Install dos2unix to handle line endings
RUN apt-get update && apt-get install -y dos2unix && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy all files
COPY . .

# Fix line endings and make gradlew executable
RUN dos2unix gradlew && chmod +x gradlew

# Build the application
RUN ./gradlew buildFatJar --no-daemon

# Copy the built jar to the working directory
RUN cp build/libs/coffee-server-fat.jar ./coffee-server.jar

# Expose the port the app runs on
EXPOSE 8080

# Run the application
CMD ["java", "-jar", "coffee-server.jar"]
