# Test the coffee server API
$baseUrl = "http://localhost:8080"

Write-Host "Testing Coffee Server API..."

# Test GET /api/items (should be empty initially)
Write-Host "`nTesting GET /api/items:"
try {
    $items = Invoke-RestMethod -Uri "$baseUrl/api/items" -Method GET
    Write-Host "Items: $($items | ConvertTo-Json)"
} catch {
    Write-Host "Error: $($_.Exception.Message)"
}

# Test POST /api/items (add a new item)
Write-Host "`nTesting POST /api/items:"
try {
    $newItem = @{
        name = "Espresso"
        price = 2.50
    }
    $result = Invoke-RestMethod -Uri "$baseUrl/api/items" -Method POST -Body ($newItem | ConvertTo-Json) -ContentType "application/json"
    Write-Host "Created item: $($result | ConvertTo-Json)"
} catch {
    Write-Host "Error: $($_.Exception.Message)"
}

# Test GET /api/items again (should now have the item)
Write-Host "`nTesting GET /api/items again:"
try {
    $items = Invoke-RestMethod -Uri "$baseUrl/api/items" -Method GET
    Write-Host "Items: $($items | ConvertTo-Json)"
} catch {
    Write-Host "Error: $($_.Exception.Message)"
}

# Test GET /api/orders
Write-Host "`nTesting GET /api/orders:"
try {
    $orders = Invoke-RestMethod -Uri "$baseUrl/api/orders" -Method GET
    Write-Host "Orders: $($orders | ConvertTo-Json)"
} catch {
    Write-Host "Error: $($_.Exception.Message)"
}

Write-Host "`nAPI tests completed!"
